"""
Simple Spinning Ball in Square (Console Version)
A Python script that displays a ball spinning inside a square using console characters.
"""

import math
import time
import os
import sys

class ConsoleSpinningBall:
    def __init__(self, width=40, height=20):
        self.width = width
        self.height = height
        self.center_x = width // 2
        self.center_y = height // 2
        self.angle = 0
        self.angular_speed = 0.2
        self.orbit_radius_x = (width // 2) - 3
        self.orbit_radius_y = (height // 2) - 2
        
    def clear_screen(self):
        """Clear the console screen"""
        os.system('cls' if os.name == 'nt' else 'clear')
        
    def get_ball_position(self):
        """Calculate current ball position"""
        x = self.center_x + self.orbit_radius_x * math.cos(self.angle)
        y = self.center_y + self.orbit_radius_y * math.sin(self.angle)
        return (int(x), int(y))
        
    def draw_frame(self):
        """Draw a single frame of the animation"""
        ball_x, ball_y = self.get_ball_position()
        
        # Create the frame
        frame = []
        for y in range(self.height):
            row = []
            for x in range(self.width):
                # Draw square border
                if (x == 0 or x == self.width - 1 or 
                    y == 0 or y == self.height - 1):
                    row.append('█')
                # Draw ball
                elif x == ball_x and y == ball_y:
                    row.append('●')
                # Draw ball trail (previous positions)
                elif self.is_trail_position(x, y):
                    row.append('○')
                else:
                    row.append(' ')
            frame.append(''.join(row))
        
        return '\n'.join(frame)
        
    def is_trail_position(self, x, y):
        """Check if position is part of ball trail"""
        for i in range(1, 4):  # Check last 3 positions
            trail_angle = self.angle - (self.angular_speed * i)
            trail_x = self.center_x + self.orbit_radius_x * math.cos(trail_angle)
            trail_y = self.center_y + self.orbit_radius_y * math.sin(trail_angle)
            if int(trail_x) == x and int(trail_y) == y:
                return True
        return False
        
    def update(self):
        """Update ball position"""
        self.angle += self.angular_speed
        if self.angle >= 2 * math.pi:
            self.angle -= 2 * math.pi
            
    def run_animation(self, duration=30):
        """Run the animation for specified duration"""
        print("Spinning Ball in Square Animation")
        print("Press Ctrl+C to stop")
        print("-" * 40)
        time.sleep(2)
        
        try:
            start_time = time.time()
            frame_count = 0
            
            while time.time() - start_time < duration:
                self.clear_screen()
                
                # Draw frame
                frame = self.draw_frame()
                print(frame)
                
                # Show info
                elapsed = time.time() - start_time
                print(f"\nTime: {elapsed:.1f}s | Frame: {frame_count}")
                print(f"Angle: {math.degrees(self.angle):.1f}°")
                print("Press Ctrl+C to stop")
                
                # Update and wait
                self.update()
                frame_count += 1
                time.sleep(0.1)  # 10 FPS
                
        except KeyboardInterrupt:
            print("\nAnimation stopped by user")
        except Exception as e:
            print(f"Error: {e}")

def create_ascii_art_demo():
    """Create a static ASCII art demonstration"""
    art = """
    ╔══════════════════════════════════════╗
    ║                                      ║
    ║              ●                       ║
    ║          ○       ○                   ║
    ║                                      ║
    ║                                      ║
    ║                  ●                   ║
    ║                                      ║
    ║                                      ║
    ║          ○       ○                   ║
    ║              ●                       ║
    ║                                      ║
    ╚══════════════════════════════════════╝
    
    Spinning Ball Animation Demo
    ● = Current ball position
    ○ = Trail positions
    """
    return art

def main():
    """Main function"""
    print("Choose animation type:")
    print("1. Animated spinning ball")
    print("2. Static ASCII art demo")
    print("3. Exit")
    
    try:
        choice = input("Enter choice (1-3): ").strip()
        
        if choice == "1":
            print("\nStarting animated version...")
            ball = ConsoleSpinningBall()
            ball.run_animation(30)  # Run for 30 seconds
            
        elif choice == "2":
            print("\nStatic ASCII art demo:")
            print(create_ascii_art_demo())
            
        elif choice == "3":
            print("Goodbye!")
            
        else:
            print("Invalid choice!")
            
    except KeyboardInterrupt:
        print("\nProgram interrupted by user")
    except Exception as e:
        print(f"Error: {e}")

if __name__ == "__main__":
    main()
