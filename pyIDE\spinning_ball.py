"""
Spinning Ball in Square Animation
A Python script that displays a ball spinning inside a square using pygame.
"""

import pygame
import math
import sys

# Initialize pygame
pygame.init()

# Constants
WINDOW_WIDTH = 800
WINDOW_HEIGHT = 600
SQUARE_SIZE = 400
BALL_RADIUS = 30
FPS = 60

# Colors (RGB)
BLACK = (0, 0, 0)
WHITE = (255, 255, 255)
RED = (255, 0, 0)
BLUE = (0, 100, 255)
GREEN = (0, 255, 0)
GRAY = (128, 128, 128)

class SpinningBall:
    def __init__(self, center_x, center_y, radius, orbit_radius):
        self.center_x = center_x
        self.center_y = center_y
        self.radius = radius
        self.orbit_radius = orbit_radius
        self.angle = 0
        self.angular_speed = 0.05  # Radians per frame
        self.color = RED
        
    def update(self):
        """Update the ball's position"""
        self.angle += self.angular_speed
        
    def get_position(self):
        """Calculate current position based on angle"""
        x = self.center_x + self.orbit_radius * math.cos(self.angle)
        y = self.center_y + self.orbit_radius * math.sin(self.angle)
        return (int(x), int(y))
        
    def draw(self, screen):
        """Draw the ball at current position"""
        pos = self.get_position()
        pygame.draw.circle(screen, self.color, pos, self.radius)
        # Add a white highlight for 3D effect
        highlight_pos = (pos[0] - self.radius//3, pos[1] - self.radius//3)
        pygame.draw.circle(screen, WHITE, highlight_pos, self.radius//4)

class Square:
    def __init__(self, center_x, center_y, size):
        self.center_x = center_x
        self.center_y = center_y
        self.size = size
        self.color = BLUE
        self.border_width = 5
        
    def get_rect(self):
        """Get the rectangle coordinates"""
        left = self.center_x - self.size // 2
        top = self.center_y - self.size // 2
        return pygame.Rect(left, top, self.size, self.size)
        
    def draw(self, screen):
        """Draw the square"""
        rect = self.get_rect()
        # Draw filled square with transparency effect
        pygame.draw.rect(screen, (*self.color, 50), rect)
        # Draw border
        pygame.draw.rect(screen, self.color, rect, self.border_width)

def main():
    """Main game loop"""
    # Create the display window
    screen = pygame.display.set_mode((WINDOW_WIDTH, WINDOW_HEIGHT))
    pygame.display.set_caption("Spinning Ball in Square")
    clock = pygame.time.Clock()
    
    # Calculate center of screen
    center_x = WINDOW_WIDTH // 2
    center_y = WINDOW_HEIGHT // 2
    
    # Create square and ball
    square = Square(center_x, center_y, SQUARE_SIZE)
    
    # Ball orbits inside the square (orbit radius is less than square size)
    orbit_radius = (SQUARE_SIZE // 2) - BALL_RADIUS - 20
    ball = SpinningBall(center_x, center_y, BALL_RADIUS, orbit_radius)
    
    # Font for instructions
    font = pygame.font.Font(None, 36)
    small_font = pygame.font.Font(None, 24)
    
    running = True
    paused = False
    
    while running:
        # Handle events
        for event in pygame.event.get():
            if event.type == pygame.QUIT:
                running = False
            elif event.type == pygame.KEYDOWN:
                if event.key == pygame.K_ESCAPE:
                    running = False
                elif event.key == pygame.K_SPACE:
                    paused = not paused
                elif event.key == pygame.K_UP:
                    ball.angular_speed += 0.01
                elif event.key == pygame.K_DOWN:
                    ball.angular_speed = max(0.01, ball.angular_speed - 0.01)
                elif event.key == pygame.K_r:
                    # Reset
                    ball.angle = 0
                    ball.angular_speed = 0.05
        
        # Update game state
        if not paused:
            ball.update()
        
        # Draw everything
        screen.fill(BLACK)
        
        # Draw square
        square.draw(screen)
        
        # Draw ball
        ball.draw(screen)
        
        # Draw trail effect (optional - creates a cool visual)
        trail_positions = []
        temp_angle = ball.angle
        for i in range(10):
            temp_angle -= ball.angular_speed * 2
            trail_x = center_x + orbit_radius * math.cos(temp_angle)
            trail_y = center_y + orbit_radius * math.sin(temp_angle)
            alpha = 255 - (i * 25)
            if alpha > 0:
                trail_color = (*ball.color, alpha)
                trail_radius = max(1, ball.radius - i * 2)
                pygame.draw.circle(screen, ball.color, 
                                 (int(trail_x), int(trail_y)), 
                                 trail_radius)
        
        # Draw instructions
        title_text = font.render("Spinning Ball in Square", True, WHITE)
        screen.blit(title_text, (10, 10))
        
        instructions = [
            "SPACE - Pause/Resume",
            "UP/DOWN - Change speed",
            "R - Reset",
            "ESC - Exit"
        ]
        
        for i, instruction in enumerate(instructions):
            text = small_font.render(instruction, True, GRAY)
            screen.blit(text, (10, 60 + i * 25))
        
        # Show current speed
        speed_text = small_font.render(f"Speed: {ball.angular_speed:.3f}", True, WHITE)
        screen.blit(speed_text, (10, 180))
        
        if paused:
            pause_text = font.render("PAUSED", True, GREEN)
            text_rect = pause_text.get_rect(center=(center_x, center_y - 100))
            screen.blit(pause_text, text_rect)
        
        # Update display
        pygame.display.flip()
        clock.tick(FPS)
    
    # Quit
    pygame.quit()
    sys.exit()

if __name__ == "__main__":
    try:
        main()
    except pygame.error as e:
        print(f"Pygame error: {e}")
        print("Make sure pygame is installed: pip install pygame")
    except Exception as e:
        print(f"Error: {e}")
