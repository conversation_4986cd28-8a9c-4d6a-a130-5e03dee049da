--- main.py (original)+++ main.py (new)@@ -1,399 +1 @@-"""                                                           │
│     2 -PyIDE Main Application                                                                                              │
│     3 -                                                                                                                    │
│     4 -This is the main entry point for PyIDE that orchestrates:                                                           │
│     5 -- Configuration loading                                                                                             │
│     6 -- Component initialization                                                                                          │
│     7 -- AI client and tool setup                                                                                          │
│     8 -- Main interaction loop                                                                                             │
│     9 -- Error handling and logging                                                                                        │
│    10 -"""                                                                                                                 │
│    11 -                                                                                                                    │
│    12 -import asyncio                                                                                                      │
│    13 -import json                                                                                                         │
│    14 -import logging                                                                                                      │
│    15 -import sys                                                                                                          │
│    16 -from pathlib import Path                                                                                            │
│    17 -from typing import Dict, Any, Optional                                                                              │
│    18 -                                                                                                                    │
│    19 -# Core imports                                                                                                      │
│    20 -from core.ai_client import AIClient                                                                                 │
│    21 -from core.parser import Response<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Text<PERSON>ontent                                                        │
│    22 -from core.security.ignore import IgnoreController                                                                   │
│    23 -from core.security.validator import SecurityValidator                                                               │
│    24 -from core.tools.file_ops import FileOperations                                                                      │
│    25 -from core.tools.command import CommandExecutor                                                                      │
│    26 -from core.tools.search import FileSearcher                                                                          │
│    27 -from core.ui.cli import <PERSON><PERSON><PERSON><PERSON>face                                                                                │
│    28 -from core.ui.diff_view import Di<PERSON><PERSON><PERSON><PERSON>                                                                            │
│    29 -                                                                                                                    │
│    30 -                                                                                                                    │
│    31 -class PyIDE:                                                                                                        │
│    32 -    """                                                                                                             │
│    33 -    Main PyIDE application class                                                                                    │
│    34 -    """                                                                                                             │
│    35 -                                                                                                                    │
│    36 -    def __init__(self, config_path: str = "config.json"):                                                           │
│    37 -        self.config_path = config_path                                                                              │
│    38 -        self.config: Dict[str, Any] = {}                                                                            │
│    39 -        self.workspace_path: Optional[Path] = None                                                                  │
│    40 -                                                                                                                    │
│    41 -        # Core components                                                                                           │
│    42 -        self.ai_client: Optional[AIClient] = None                                                                   │
│    43 -        self.parser: Optional[ResponseParser] = None                                                                │
│    44 -        self.ignore_controller: Optional[IgnoreController] = None                                                   │
│    45 -        self.security_validator: Optional[SecurityValidator] = None                                                 │
│    46 -                                                                                                                    │
│    47 -        # Tools                                                                                                     │
│    48 -        self.file_ops: Optional[FileOperations] = None                                                              │
│    49 -        self.command_executor: Optional[CommandExecutor] = None                                                     │
│    50 -        self.file_searcher: Optional[FileSearcher] = None                                                           │
│    51 -                                                                                                                    │
│    52 -        # UI                                                                                                        │
│    53 -        self.cli: Optional[CLIInterface] = None                                                                     │
│    54 -        self.diff_viewer: Optional[DiffViewer] = None                                                               │
│    55 -                                                                                                                    │
│    56 -        # State                                                                                                     │
│    57 -        self.running = False                                                                                        │
│    58 -                                                                                                                    │
│    59 -    async def initialize(self):                                                                                     │
│    60 -        """Initialize all components"""                                                                             │
│    61 -        try:                                                                                                        │
│    62 -            # Load configuration                                                                                    │
│    63 -            await self._load_config()                                                                               │
│    64 -                                                                                                                    │
│    65 -            # Setup logging                                                                                         │
│    66 -            self._setup_logging()                                                                                   │
│    67 -                                                                                                                    │
│    68 -            # Initialize workspace                                                                                  │
│    69 -            self._setup_workspace()                                                                                 │
│    70 -                                                                                                                    │
│    71 -            # Initialize security components                                                                        │
│    72 -            self._initialize_security()                                                                             │
│    73 -                                                                                                                    │
│    74 -            # Initialize tools                                                                                      │
│    75 -            self._initialize_tools()                                                                                │
│    76 -                                                                                                                    │
│    77 -            # Initialize UI                                                                                         │
│    78 -            self._initialize_ui()                                                                                   │
│    79 -                                                                                                                    │
│    80 -            # Initialize AI client                                                                                  │
│    81 -            await self._initialize_ai_client()                                                                      │
│    82 -                                                                                                                    │
│    83 -            # Initialize parser                                                                                     │
│    84 -            self.parser = ResponseParser()                                                                          │
│    85 -                                                                                                                    │
│    86 -            logging.info("PyIDE initialized successfully")                                                          │
│    87 -                                                                                                                    │
│    88 -        except Exception as e:                                                                                      │
│    89 -            logging.error(f"Failed to initialize PyIDE: {e}")                                                       │
│    90 -            raise                                                                                                   │
│    91 -                                                                                                                    │
│    92 -    async def _load_config(self):                                                                                   │
│    93 -        """Load configuration from file"""                                                                          │
│    94 -        config_file = Path(self.config_path)                                                                        │
│    95 -                                                                                                                    │
│    96 -        if not config_file.exists():                                                                                │
│    97 -            raise FileNotFoundError(f"Configuration file not found: {self.config_path}")                            │
│    98 -                                                                                                                    │
│    99 -        try:                                                                                                        │
│   100 -            with open(config_file, 'r', encoding='utf-8') as f:                                                     │
│   101 -                self.config = json.load(f)                                                                          │
│   102 -        except json.JSONDecodeError as e:                                                                           │
│   103 -            raise ValueError(f"Invalid JSON in configuration file: {e}")                                            │
│   104 -                                                                                                                    │
│   105 -    def _setup_logging(self):                                                                                       │
│   106 -        """Setup logging configuration"""                                                                           │
│   107 -        log_config = self.config.get("logging", {})                                                                 │
│   108 -                                                                                                                    │
│   109 -        logging.basicConfig(                                                                                        │
│   110 -            level=getattr(logging, log_config.get("level", "INFO")),                                                │
│   111 -            format=log_config.get("format", "%(asctime)s - %(name)s - %(levelname)s - %(message)s"),                │
│   112 -            handlers=[                                                                                              │
│   113 -                logging.StreamHandler(sys.stdout),                                                                  │
│   114 -                logging.FileHandler(log_config.get("file", "pyide.log"))                                            │
│   115 -            ]                                                                                                       │
│   116 -        )                                                                                                           │
│   117 -                                                                                                                    │
│   118 -    def _setup_workspace(self):                                                                                     │
│   119 -        """Setup workspace directory"""                                                                             │
│   120 -        workspace_config = self.config.get("workspace", {})                                                         │
│   121 -        workspace_dir = workspace_config.get("default_directory", ".")                                              │
│   122 -                                                                                                                    │
│   123 -        self.workspace_path = Path(workspace_dir).resolve()                                                         │
│   124 -                                                                                                                    │
│   125 -        if not self.workspace_path.exists():                                                                        │
│   126 -            self.workspace_path.mkdir(parents=True, exist_ok=True)                                                  │
│   127 -                                                                                                                    │
│   128 -        logging.info(f"Workspace: {self.workspace_path}")                                                           │
│   129 -                                                                                                                    │
│   130 -    def _initialize_security(self):                                                                                 │
│   131 -        """Initialize security components"""                                                                        │
│   132 -        ignore_file = self.config.get("security", {}).get("ignore_file", ".pyideignore")                            │
│   133 -                                                                                                                    │
│   134 -        self.ignore_controller = IgnoreController(                                                                  │
│   135 -            str(self.workspace_path),                                                                               │
│   136 -            ignore_file                                                                                             │
│   137 -        )                                                                                                           │
│   138 -                                                                                                                    │
│   139 -        self.security_validator = SecurityValidator(self.config)                                                    │
│   140 -                                                                                                                    │
│   141 -    def _initialize_tools(self):                                                                                    │
│   142 -        """Initialize tool components"""                                                                            │
│   143 -        self.file_ops = FileOperations(                                                                             │
│   144 -            self.config,                                                                                            │
│   145 -            self.ignore_controller,                                                                                 │
│   146 -            self.security_validator                                                                                 │
│   147 -        )                                                                                                           │
│   148 -                                                                                                                    │
│   149 -        self.command_executor = CommandExecutor(                                                                    │
│   150 -            self.config,                                                                                            │
│   151 -            self.ignore_controller,                                                                                 │
│   152 -            self.security_validator                                                                                 │
│   153 -        )                                                                                                           │
│   154 -                                                                                                                    │
│   155 -        self.file_searcher = FileSearcher(                                                                          │
│   156 -            self.config,                                                                                            │
│   157 -            self.ignore_controller                                                                                  │
│   158 -        )                                                                                                           │
│   159 -                                                                                                                    │
│   160 -    def _initialize_ui(self):                                                                                       │
│   161 -        """Initialize UI components"""                                                                              │
│   162 -        self.cli = CLIInterface(self.config)                                                                        │
│   163 -        self.diff_viewer = DiffViewer(self.config)                                                                  │
│   164 -                                                                                                                    │
│   165 -    async def _initialize_ai_client(self):                                                                          │
│   166 -        """Initialize AI client"""                                                                                  │
│   167 -        self.ai_client = AIClient(self.config)                                                                      │
│   168 -                                                                                                                    │
│   169 -    async def run(self):                                                                                            │
│   170 -        """Main application loop"""                                                                                 │
│   171 -        self.running = True                                                                                         │
│   172 -                                                                                                                    │
│   173 -        try:                                                                                                        │
│   174 -            # Display welcome message                                                                               │
│   175 -            self.cli.display_welcome()                                                                              │
│   176 -                                                                                                                    │
│   177 -            # Main interaction loop                                                                                 │
│   178 -            while self.running:                                                                                     │
│   179 -                try:                                                                                                │
│   180 -                    # Get user input                                                                                │
│   181 -                    user_input = self.cli.get_user_input()                                                          │
│   182 -                                                                                                                    │
│   183 -                    if not user_input.strip():                                                                      │
│   184 -                        continue                                                                                    │
│   185 -                                                                                                                    │
│   186 -                    # Handle special commands                                                                       │
│   187 -                    if user_input.startswith('/'):                                                                  │
│   188 -                        await self._handle_special_command(user_input)                                              │
│   189 -                        continue                                                                                    │
│   190 -                                                                                                                    │
│   191 -                    # Process AI request                                                                            │
│   192 -                    await self._process_ai_request(user_input)                                                      │
│   193 -                                                                                                                    │
│   194 -                except KeyboardInterrupt:                                                                           │
│   195 -                    self.cli.display_info("Use /exit to quit")                                                      │
│   196 -                    continue                                                                                        │
│   197 -                except Exception as e:                                                                              │
│   198 -                    self.cli.display_error(f"Unexpected error: {e}")                                                │
│   199 -                    logging.error(f"Unexpected error in main loop: {e}")                                            │
│   200 -                    continue                                                                                        │
│   201 -                                                                                                                    │
│   202 -        except Exception as e:                                                                                      │
│   203 -            self.cli.display_error(f"Fatal error: {e}")                                                             │
│   204 -            logging.error(f"Fatal error: {e}")                                                                      │
│   205 -        finally:                                                                                                    │
│   206 -            await self._cleanup()                                                                                   │
│   207 -                                                                                                                    │
│   208 -    async def _handle_special_command(self, command: str):                                                          │
│   209 -        """Handle special commands like /help, /exit, etc."""                                                       │
│   210 -        command = command.lower().strip()                                                                           │
│   211 -                                                                                                                    │
│   212 -        if command == "/exit" or command == "/quit":                                                                │
│   213 -            self.cli.display_info("Goodbye!")                                                                       │
│   214 -            self.running = False                                                                                    │
│   215 -        elif command == "/help":                                                                                    │
│   216 -            self.cli.display_help()                                                                                 │
│   217 -        elif command == "/clear":                                                                                   │
│   218 -            self.cli.clear_screen()                                                                                 │
│   219 -            if self.ai_client:                                                                                      │
│   220 -                self.ai_client.clear_history()                                                                      │
│   221 -            self.cli.display_success("Conversation history cleared")                                                │
│   222 -        elif command == "/status":                                                                                  │
│   223 -            await self._show_status()                                                                               │
│   224 -        elif command == "/config":                                                                                  │
│   225 -            await self._show_config()                                                                               │
│   226 -        else:                                                                                                       │
│   227 -            self.cli.display_error(f"Unknown command: {command}")                                                   │
│   228 -                                                                                                                    │
│   229 -    async def _process_ai_request(self, user_input: str):                                                           │
│   230 -        """Process AI request and handle tool calls"""                                                              │
│   231 -        try:                                                                                                        │
│   232 -            self.cli.display_ai_response_start()                                                                    │
│   233 -                                                                                                                    │
│   234 -            # Stream AI response                                                                                    │
│   235 -            async with self.ai_client as client:                                                                    │
│   236 -                full_response = ""                                                                                  │
│   237 -                                                                                                                    │
│   238 -                async for chunk in client.stream_request(user_input, str(self.workspace_path)):                     │
│   239 -                    if chunk.type == "text":                                                                        │
│   240 -                        self.cli.display_ai_text(chunk.content)                                                     │
│   241 -                        full_response += chunk.content                                                              │
│   242 -                    elif chunk.type == "error":                                                                     │
│   243 -                        self.cli.display_error(f"AI Error: {chunk.error}")                                          │
│   244 -                        return                                                                                      │
│   245 -                    elif chunk.type == "usage":                                                                     │
│   246 -                        # Log token usage                                                                           │
│   247 -                        usage = chunk.usage                                                                         │
│   248 -                        logging.info(f"Token usage: {usage}")                                                       │
│   249 -                                                                                                                    │
│   250 -                # Parse response for tool calls                                                                     │
│   251 -                content_blocks = self.parser.parse_complete(full_response)                                          │
│   252 -                tool_calls = self.parser.extract_tool_calls(content_blocks)                                         │
│   253 -                                                                                                                    │
│   254 -                # Execute tool calls                                                                                │
│   255 -                for tool_call in tool_calls:                                                                        │
│   256 -                    if self.parser.validate_tool_call(tool_call):                                                   │
│   257 -                        await self._execute_tool_call(tool_call)                                                    │
│   258 -                    else:                                                                                           │
│   259 -                        self.cli.display_error(f"Invalid tool call: {tool_call.name}")                              │
│   260 -                                                                                                                    │
│   261 -        except Exception as e:                                                                                      │
│   262 -            self.cli.display_error(f"Error processing AI request: {e}")                                             │
│   263 -            logging.error(f"Error processing AI request: {e}")                                                      │
│   264 -                                                                                                                    │
│   265 -    async def _execute_tool_call(self, tool_call: ToolUse):                                                         │
│   266 -        """Execute a tool call"""                                                                                   │
│   267 -        try:                                                                                                        │
│   268 -            self.cli.display_tool_call(tool_call.name, tool_call.params)                                            │
│   269 -                                                                                                                    │
│   270 -            result = None                                                                                           │
│   271 -            success = False                                                                                         │
│   272 -                                                                                                                    │
│   273 -            # Execute based on tool name                                                                            │
│   274 -            if tool_call.name == "read_file":                                                                       │
│   275 -                result = await self.file_ops.read_file(tool_call.params.get("path", ""))                            │
│   276 -                success = result.get("success", False)                                                              │
│   277 -                                                                                                                    │
│   278 -            elif tool_call.name == "write_to_file":                                                                 │
│   279 -                result = await self.file_ops.write_file(                                                            │
│   280 -                    tool_call.params.get("path", ""),                                                               │
│   281 -                    tool_call.params.get("content", "")                                                             │
│   282 -                )                                                                                                   │
│   283 -                success = result.get("success", False)                                                              │
│   284 -                                                                                                                    │
│   285 -            elif tool_call.name == "replace_in_file":                                                               │
│   286 -                result = await self.file_ops.replace_in_file(                                                       │
│   287 -                    tool_call.params.get("path", ""),                                                               │
│   288 -                    tool_call.params.get("diff", "")                                                                │
│   289 -                )                                                                                                   │
│   290 -                success = result.get("success", False)                                                              │
│   291 -                                                                                                                    │
│   292 -            elif tool_call.name == "list_files":                                                                    │
│   293 -                recursive = tool_call.params.get("recursive", "false").lower() == "true"                            │
│   294 -                result = await self.file_ops.list_files(                                                            │
│   295 -                    tool_call.params.get("path", "."),                                                              │
│   296 -                    recursive=recursive                                                                             │
│   297 -                )                                                                                                   │
│   298 -                success = result.get("success", False)                                                              │
│   299 -                                                                                                                    │
│   300 -            elif tool_call.name == "search_files":                                                                  │
│   301 -                result = await self.file_searcher.search_files(                                                     │
│   302 -                    tool_call.params.get("path", "."),                                                              │
│   303 -                    tool_call.params.get("regex", ""),                                                              │
│   304 -                    tool_call.params.get("file_pattern", "*")                                                       │
│   305 -                )                                                                                                   │
│   306 -                success = result.get("success", False)                                                              │
│   307 -                                                                                                                    │
│   308 -            elif tool_call.name == "execute_command":                                                               │
│   309 -                result = await self.command_executor.execute_command(                                               │
│   310 -                    tool_call.params.get("command", "")                                                             │
│   311 -                )                                                                                                   │
│   312 -                success = result.get("success", False)                                                              │
│   313 -                                                                                                                    │
│   314 -            elif tool_call.name == "ask_followup_question":                                                         │
│   315 -                question = tool_call.params.get("question", "")                                                     │
│   316 -                self.cli.display_info(f"AI Question: {question}")                                                   │
│   317 -                answer = self.cli.get_user_input()                                                                  │
│   318 -                result = {"success": True, "answer": answer}                                                        │
│   319 -                success = True                                                                                      │
│   320 -                                                                                                                    │
│   321 -            else:                                                                                                   │
│   322 -                result = {"success": False, "error": f"Unknown tool: {tool_call.name}"}                             │
│   323 -                success = False                                                                                     │
│   324 -                                                                                                                    │
│   325 -            # Display result                                                                                        │
│   326 -            if result:                                                                                              │
│   327 -                self.cli.display_tool_result(tool_call.name, result, success)                                       │
│   328 -                                                                                                                    │
│   329 -                # Add tool result to conversation history                                                           │
│   330 -                if self.ai_client:                                                                                  │
│   331 -                    tool_result_text = self.parser.format_tool_result(                                              │
│   332 -                        tool_call.name,                                                                             │
│   333 -                        result.get("content", str(result)),                                                         │
│   334 -                        success                                                                                     │
│   335 -                    )                                                                                               │
│   336 -                    self.ai_client.add_message("user", tool_result_text)                                            │
│   337 -                                                                                                                    │
│   338 -        except Exception as e:                                                                                      │
│   339 -            self.cli.display_error(f"Error executing tool {tool_call.name}: {e}")                                   │
│   340 -            logging.error(f"Error executing tool {tool_call.name}: {e}")                                            │
│   341 -                                                                                                                    │
│   342 -    async def _show_status(self):                                                                                   │
│   343 -        """Show system status"""                                                                                    │
│   344 -        status_info = {                                                                                             │
│   345 -            "Workspace": {                                                                                          │
│   346 -                "status": "ok" if self.workspace_path and self.workspace_path.exists() else "error",                │
│   347 -                "details": str(self.workspace_path) if self.workspace_path else "Not set"                           │
│   348 -            },                                                                                                      │
│   349 -            "AI Client": {                                                                                          │
│   350 -                "status": "ok" if self.ai_client else "error",                                                      │
│   351 -                "details": self.config.get("api", {}).get("model", "Not configured")                                │
│   352 -            },                                                                                                      │
│   353 -            "Security": {                                                                                           │
│   354 -                "status": "ok" if self.ignore_controller else "error",                                              │
│   355 -                "details": f"Ignore file: {self.ignore_controller.has_ignore_file()}" if self.ignore_controller els │
│   356 -            },                                                                                                      │
│   357 -            "Tools": {                                                                                              │
│   358 -                "status": "ok" if all([self.file_ops, self.command_executor, self.file_searcher]) else "error",     │
│   359 -                "details": "All tools loaded" if all([self.file_ops, self.command_executor, self.file_searcher]) el │
│   360 -            }                                                                                                       │
│   361 -        }                                                                                                           │
│   362 -                                                                                                                    │
│   363 -        self.cli.display_status(status_info)                                                                        │
│   364 -                                                                                                                    │
│   365 -    async def _show_config(self):                                                                                   │
│   366 -        """Show current configuration"""                                                                            │
│   367 -        # Redact sensitive information                                                                              │
│   368 -        safe_config = self.config.copy()                                                                            │
│   369 -        if "api" in safe_config and "key" in safe_config["api"]:                                                    │
│   370 -            key = safe_config["api"]["key"]                                                                         │
│   371 -            safe_config["api"]["key"] = key[:10] + "..." + key[-4:] if len(key) > 14 else "***"                     │
│   372 -                                                                                                                    │
│   373 -        config_text = json.dumps(safe_config, indent=2)                                                             │
│   374 -        self.cli.console.print(f"[bold]Current Configuration:[/bold]\n{config_text}")                               │
│   375 -                                                                                                                    │
│   376 -    async def _cleanup(self):                                                                                       │
│   377 -        """Cleanup resources"""                                                                                     │
│   378 -        if self.ai_client:                                                                                          │
│   379 -            # AI client cleanup is handled by context manager                                                       │
│   380 -            pass                                                                                                    │
│   381 -                                                                                                                    │
│   382 -        logging.info("PyIDE shutdown complete")                                                                     │
│   383 -                                                                                                                    │
│   384 -                                                                                                                    │
│   385 -async def main():                                                                                                   │
│   386 -    """Main entry point"""                                                                                          │
│   387 -    try:                                                                                                            │
│   388 -        app = PyIDE()                                                                                               │
│   389 -        await app.initialize()                                                                                      │
│   390 -        await app.run()                                                                                             │
│   391 -    except KeyboardInterrupt:                                                                                       │
│   392 -        print("\nShutdown requested by user")                                                                       │
│   393 -    except Exception as e:                                                                                          │
│   394 -        print(f"Fatal error: {e}")                                                                                  │
│   395 -        sys.exit(1)                                                                                                 │
│   396 -                                                                                                                    │
│   397 -                                                                                                                    │
│   398 -if __name__ == "__main__":                                                                                          │
│   399 -    asyncio.run(main())  